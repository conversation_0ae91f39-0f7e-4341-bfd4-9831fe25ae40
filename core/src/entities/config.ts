import Ajv from 'ajv';
import { JSONSchema7 } from 'json-schema';
import { cloneDeep, get, isEmpty, isEqual, keys, merge, omit, set, toPath, unset } from 'lodash';
import { decrypt, encrypt } from '../lib/aes';
import isEncryptedData from '../lib/is-encrypted-data';
import bookSchema from '../schema/config.json';

interface ConfigValues {
    root?: string;
    title?: string;
    description?: string;
    author?: {
        name: string;
        email: string;
        home: string;
    };
    theme: string;
    themeConfig: {
        defaultMode: 'light' | 'dark';
        primaryColor: string;
        expandLevel: number;
        navs: {
            title: string;
            url: string;
        }[];
    };
    plugins: string[];
    pluginsConfig: {
        [name: string]: any;
    };
    pdf: {
        fontFamily: string;
        margin: {
            right: number;
            left: number;
            top: number;
            bottom: number;
        };
        paperSize: 'a0' | 'a1' | 'a2' | 'a3' | 'a4' | 'a5' | 'a6' | 'b0' | 'b1' | 'b2' | 'b3' | 'b4' | 'b5' | 'b6' | 'legal' | 'letter';
        customSize?: string;
        chapterMark: 'pagebreak' | 'rule' | 'both' | 'none';
        pageBreaksBefore: string;
        pageNumbers: boolean;
    };
    release: {
        pathEncode: boolean;
    };
    assistant: {
        token?: string;
    };

    [name: string]: any;
}

function clearEmptyPath(obj: any, path: string[]) {
    const parentObjects = [];
    let _obj = obj;
    for (let i = 0; i < path.length - 1; i++) {
        parentObjects.push(_obj);
        _obj = _obj[path[i]];
    }

    for (let i = path.length - 2; i >= 0; i--) {
        if (isEmpty(parentObjects[i][path[i]])) {
            delete parentObjects[i][path[i]];
        }
    }
}

export class BaseConfig<CT extends object = ConfigValues> {

    protected key?: string;
    protected values: CT;
    protected presetValues?: CT;
    protected readonly schema: object;

    constructor(values: CT, schema: object, key?: string, presetValues?: CT) {
        this.values = merge(values, presetValues);
        this.presetValues = presetValues;
        this.schema = schema;
        this.key = key;
    }

    setKey(key: string) {
        this.key = key;
    }

    getValues() {
        return this.values;
    }

    setValues(values: any) {
        const ajv = new Ajv({ removeAdditional: true, useDefaults: true, allErrors: true, unknownFormats: 'ignore' });

        const validate = ajv.compile(this.schema);

        values = cloneDeep(values);

        const valid = validate(values);
        if (!valid) {
            for (const error of validate.errors!) {
                values = omit(values, error.dataPath.substr(1).split('/'));
            }
        }

        this.values = values;
    }

    getPresetValues() {
        return this.presetValues;
    }

    getValue<TKey extends keyof CT>(path: TKey | [TKey]): Exclude<CT[TKey], undefined>;
    getValue<TKey extends keyof CT, TDefault>(path: TKey | [TKey], defaultValue: TDefault): Exclude<CT[TKey], undefined> | TDefault;
    getValue<TKey1 extends keyof CT, TKey2 extends keyof CT[TKey1]>(path: [TKey1, TKey2]): Exclude<CT[TKey1][TKey2], undefined>;
    getValue<TKey1 extends keyof CT, TKey2 extends keyof CT[TKey1], TDefault>(path: [TKey1, TKey2], defaultValue: TDefault): Exclude<CT[TKey1][TKey2], undefined> | TDefault;
    getValue<TKey1 extends keyof CT, TKey2 extends keyof CT[TKey1], TKey3 extends keyof CT[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): Exclude<CT[TKey1][TKey2][TKey3], undefined>;
    getValue<TKey1 extends keyof CT, TKey2 extends keyof CT[TKey1], TKey3 extends keyof CT[TKey1][TKey2], TDefault>(path: [TKey1, TKey2, TKey3], defaultValue: TDefault): Exclude<CT[TKey1][TKey2][TKey3], undefined> | TDefault;
    getValue(path: any, defaultValue?: any) {
        return get(this.values, path, defaultValue);
    }

    setValue<TKey extends keyof CT>(path: TKey | [TKey], value: CT[TKey]): void;
    setValue<TKey1 extends keyof CT, TKey2 extends keyof CT[TKey1]>(path: [TKey1, TKey2], value: CT[TKey1][TKey2]): void;
    setValue<TKey1 extends keyof CT, TKey2 extends keyof CT[TKey1], TKey3 extends keyof CT[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], value: CT[TKey1][TKey2][TKey3]): void;
    setValue(path: any, value: any) {
        return set(this.values, path, value);
    }

    toObject() {
        let values = cloneDeep(this.values);

        //去除预置数据
        if (this.presetValues) {
            values = omit(values, keys(this.presetValues)) as CT;
        }

        const ajv = new Ajv({ removeAdditional: true, useDefaults: true, allErrors: true, unknownFormats: ['color'] });

        ajv.removeKeyword('default');
        ajv.addKeyword('default', {
            validate(value, data, _, currentDataPath, parentDataObject, propertyName, rootObject) {
                if (isEqual(value, data)) {
                    delete parentDataObject![propertyName as keyof typeof parentDataObject];
                    // parent object is empty
                    if (isEmpty(parentDataObject)) {
                        clearEmptyPath(rootObject, toPath(currentDataPath!.substring(1)));
                    }
                }
                return true;
            },
            modifying: true,
            errors: false
        });

        //加密数据
        ajv.addKeyword('encrypt', {
            validate: (value, data, _, __, parentDataObject, propertyName) => {
                if (this.key && value && !isEncryptedData(data) && parentDataObject && propertyName) {
                    set(parentDataObject, propertyName, `@encrypted:${encrypt(data, this.key)}`);
                }

                return true;
            },
            modifying: true,
            errors: false
        });

        const validate = ajv.compile(this.schema);

        validate(values);

        return values;
    };

    toText() {
        const obj = this.toObject();
        let text = '';
        if (!isEmpty(obj)) {
            text = JSON.stringify(obj, null, 4);
        }
        return text;
    }

    static createFromObject(values: any, schema: object, key?: string, presetValues?: any) {
        const ajv = new Ajv({ removeAdditional: true, useDefaults: true, allErrors: true, unknownFormats: ['color'] });

        //解密
        ajv.addKeyword('encrypt', {
            validate: (value, data, _, __, parentDataObject, propertyName) => {
                if (key && value && isEncryptedData(data) && parentDataObject && propertyName) {
                    set(parentDataObject, propertyName, decrypt(data.substring(11), key));
                }

                return true;
            },
            modifying: true,
            errors: false
        });

        const validate = ajv.compile(schema);

        const valid = validate(values);
        if (!valid) {
            for (const error of validate.errors!) {
                values = omit(values, error.dataPath.substring(1).split('/'));
            }
        }

        return new this(values, schema, key, presetValues);
    }
}

export default class Config extends BaseConfig<ConfigValues> {

    static file = 'book.json';
    static schema: JSONSchema7 = bookSchema as JSONSchema7;

    protected pluginsConfig: {
        [index: string]: BaseConfig<any>;
    } = {};

    protected presetPlugins: Record<string, object> = {};

    setPresetPlugins(plugins: Record<string, object>) {
        this.presetPlugins = plugins;
    }

    getPresetPluginConfig(name: string) {
        return this.presetPlugins[name];
    }

    getPlugins() {
        return Array.from(new Set([...this.getValue('plugins'), ...Object.keys(this.presetPlugins)]));
    }

    getPluginConfig(name: string, schema: object) {
        if (!this.pluginsConfig[name]) {
            const value = this.getValue(['pluginsConfig', name], {});
            this.pluginsConfig[name] = BaseConfig.createFromObject(cloneDeep(value), schema, `${this.key}:${name}`, this.presetPlugins[name]);
        }
        return this.pluginsConfig[name];
    }

    setPluginConfig(name: string, value: BaseConfig<any>) {
        const obj = value.toObject();
        if (isEmpty(obj)) {
            unset(this.values, ['pluginsConfig', name]);
        } else {
            this.setValue(['pluginsConfig', name], obj);
        }
        delete this.pluginsConfig[name];
    }

    static createFromObject(value: any): Config {
        return super.createFromObject(value, Config.schema) as Config;
    }

}
