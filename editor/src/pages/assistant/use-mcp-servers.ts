import { useAsync } from '@topwrite/common';
import { useCallback, useEffect } from 'react';
import { socket } from '../../lib/socket';
import { <PERSON>uffer } from 'buffer';

export interface McpServer {
    name: string;
    url: string;
    type?: string;
    headers?: Record<string, string>;
    tools?: McpServerTool[];
}

export interface McpServerTool {
    name: string;
    title: string;
    description: string;
}

interface McpConfig {
    servers: McpServer[];
}

export default function useMcpServers() {
    const { result: mcp = { servers: [] }, execute } = useAsync(async () => {
        const buf = await socket.readFile('.topwrite/mcp.json');
        let mcp: McpConfig;
        try {
            mcp = JSON.parse(buf.toString());
        } catch {
            mcp = { servers: [] };
        }
        return mcp;
    }, []);

    const { servers = [] } = mcp;

    // 监控 .topwrite/mcp.json 文件变化
    useEffect(() => {
        const listener = (names: string[]) => {
            if (names.includes('.topwrite/mcp.json')) {
                execute();
            }
        };

        socket.on('file.change', listener);

        return () => {
            socket.off('file.change', listener);
        };
    }, [execute]);

    const addServer = useCallback(async (server: McpServer) => {
        try {
            const newMcp: McpConfig = {
                servers: [...servers, server]
            };
            const content = JSON.stringify(newMcp, null, 4);
            await socket.writeFile('.topwrite/mcp.json', Buffer.from(content));
        } catch (error) {
            console.error('Failed to add server:', error);
            throw error;
        }
    }, [servers]);

    const deleteServer = useCallback(async (name: string) => {
        try {
            const newMcp: McpConfig = {
                servers: servers.filter(server => server.name !== name)
            };
            const content = JSON.stringify(newMcp, null, 4);
            await socket.writeFile('.topwrite/mcp.json', Buffer.from(content));
        } catch (error) {
            console.error('Failed to delete server:', error);
            throw error;
        }
    }, [servers]);

    return { servers, addServer, deleteServer };
}
