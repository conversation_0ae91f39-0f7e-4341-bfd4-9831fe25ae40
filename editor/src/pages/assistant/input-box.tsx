import { css, styled, useActions } from '@topwrite/common';
import { useCallback, useRef, useState } from 'react';
import { BsFileText, BsTools } from 'react-icons/bs';
import { IoMdSend, IoMdSquare } from 'react-icons/io';
import TextareaAutosize from 'react-textarea-autosize';
import Tooltip from '../../components/tooltip';
import { ReactComponent as Gudelines } from '../../images/guidelines.svg';
import useOverlayState from '../../lib/use-overlay-state';
import ChangedFiles from './changed-files';
import { useContext } from './context';
import useFormatMessage from '../../lib/use-format-message';
import ToolsConfigModal from './tools-config-modal';

export default function InputBox() {
    const { setCurrent, setExtra } = useActions('workspace');
    const { send, stop, loading, filename, range } = useContext();
    const [focused, setFocused] = useState(false);
    const [query, setQuery] = useState('');
    const { show: showToolsModal, state: toolsModalState } = useOverlayState();
    const t = useFormatMessage();
    const textarea = useRef<HTMLTextAreaElement>(null);

    const sendMessage = useCallback(async () => {
        if (query) {
            setQuery('');
            await send(query);
            requestAnimationFrame(() => {
                textarea.current?.focus();
            });
        }
    }, [query, send]);

    return <>
        <Container>
            <ChangedFiles />
            <InputArea $focused={focused}>
                <ContextBar>
                    <Tooltip placement={'top'} tooltip={'Guidelines'}>
                        <Item onClick={() => {
                            setCurrent('.topwrite/rules.md');
                            setExtra(null);
                        }}>
                            <Gudelines />
                        </Item>
                    </Tooltip>
                    {filename && <Item $active={true}>
                        <BsFileText className={'me-1'} />
                        {filename}
                        {range && <>#{range.start.line}:{range.start.column}-{range.end.line}:{range.end.column}</>}
                    </Item>}
                </ContextBar>
                <div className={'d-grid'}>
                    <TextareaAutosize
                        className={'px-2 py-1'}
                        ref={textarea}
                        placeholder={t('assistant.input_placeholder')}
                        minRows={3}
                        maxRows={8}
                        disabled={loading}
                        value={query}
                        onChange={e => setQuery(e.target.value)}
                        onFocus={() => setFocused(true)}
                        onBlur={() => setFocused(false)}
                        onKeyDown={e => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                if (loading) {
                                    stop();
                                } else {
                                    sendMessage();
                                }
                            }
                        }}
                    />
                </div>
                <Toolbar>
                    <ToolbarRight>
                        <Tooltip placement={'top'} tooltip={'工具配置'}>
                            <ToolButton onClick={() => showToolsModal()}>
                                <BsTools />
                            </ToolButton>
                        </Tooltip>

                        <InputButton
                            disabled={!loading && !query}
                            $loading={loading}
                            onClick={loading ? stop : sendMessage}
                        >
                            {loading ? <IoMdSquare /> : <IoMdSend />}
                        </InputButton>
                    </ToolbarRight>
                </Toolbar>
            </InputArea>
        </Container>
        <ToolsConfigModal {...toolsModalState} />
    </>;
}

const Container = styled.div`
    background: var(--ttw-background);
    padding: 1rem;
`;

const InputArea = styled.div<{ $focused?: boolean }>`
    display: flex;
    flex-direction: column;
    border: var(--bs-border-width) var(--bs-border-style) ${props => props.$focused ? 'var(--bs-primary)' : 'var(--bs-border-color)'};
    border-radius: var(--bs-border-radius-lg);

    textarea {
        border: none;
        outline: none;
        resize: none;
        background: transparent;
        color: var(--ttw-color);

        &::placeholder {
            color: var(--bs-secondary);
        }
    }
`;

const ContextBar = styled.div`
    display: flex;
    align-items: center;
    gap: .25rem;
    background: var(--ttw-foreground);
    padding: 0.25rem 0.5rem;
    border-top-left-radius: var(--bs-border-radius-lg);
    border-top-right-radius: var(--bs-border-radius-lg);
`;

const Toolbar = styled.div`
    display: flex;
    align-items: center;
    padding: 0.5rem 0.5rem;
    border-bottom-left-radius: var(--bs-border-radius-lg);
    border-bottom-right-radius: var(--bs-border-radius-lg);
`;

const ToolbarRight = styled.div`
    display: flex;
    align-items: center;
    gap: .25rem;
    margin-left: auto;
`;

const Item = styled.div<{ $active?: boolean }>`
    font-size: 12px;
    color: var(--bs-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 .5rem;
    height: 22px;
    border-radius: 0.25rem;
    cursor: pointer;
    ${props => props.$active && css`
        background: var(--ttw-box-hover-background);
    `};

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const ToolButton = styled.button`
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    border-radius: .25rem;
    outline: none;
    border: none;
    background: transparent;
    color: var(--bs-secondary);

    &:hover {
        background: var(--ttw-box-hover-background);
        color: var(--ttw-color);
    }
`;

const InputButton = styled.button<{ $loading: boolean }>`
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    border-radius: .25rem;
    outline: none;
    border: none;
    background: var(--bs-primary);
    color: var(--bs-white);
    position: relative;

    ${props => props.$loading && css`
        color: var(--bs-danger);
        background: rgba(var(--bs-danger-rgb), .15);
    `};

    &:disabled {
        background: var(--ttw-button-disabled-background);
        color: var(--ttw-button-disabled-color);
        cursor: not-allowed;
    }

    &:hover&:not(:disabled) {
        opacity: 0.9;
    }

`;
